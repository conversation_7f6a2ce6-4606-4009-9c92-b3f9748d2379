/* ===== MOBISCROLL CALENDAR STYLING ===== */
:host {
    --mobiscroll-primary: #1976d2;
    --mobiscroll-secondary: #f5f5f5;
    --mobiscroll-accent: #ff6b35;
    --mobiscroll-text: #333333;
    --mobiscroll-border: #e0e0e0;
    --mobiscroll-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --mobiscroll-radius: 8px;
}

/* ===== MOBISCROLL CALENDAR CONTAINER ===== */
.mobiscroll-calendar-container {
    background: #ffffff;
    border-radius: var(--mobiscroll-radius);
    box-shadow: var(--mobiscroll-shadow);
    border: 1px solid var(--mobiscroll-border);
    overflow: hidden;
    margin-bottom: 1rem;
}

.mobiscroll-eventcalendar {
    min-height: 600px;
    width: 100%;
}

/* ===== MOBISCROLL CUSTOM THEME OVERRIDES ===== */
/* Override Mobiscroll's default styling to match our design */
.mobiscroll-eventcalendar .mbsc-calendar-header {
    background: var(--mobiscroll-primary);
    color: white;
}

.mobiscroll-eventcalendar .mbsc-calendar-day.mbsc-selected {
    background: var(--mobiscroll-primary);
    color: white;
}

.mobiscroll-eventcalendar .mbsc-calendar-day:hover {
    background: rgba(25, 118, 210, 0.1);
}

.mobiscroll-eventcalendar .mbsc-event {
    border-radius: 4px;
    font-weight: 500;
}

/* Event type specific colors */
.mobiscroll-eventcalendar .mbsc-event[data-resource="meeting"] {
    background: #1976d2;
    color: white;
}

.mobiscroll-eventcalendar .mbsc-event[data-resource="deadline"] {
    background: #d32f2f;
    color: white;
}

.mobiscroll-eventcalendar .mbsc-event[data-resource="presentation"] {
    background: #388e3c;
    color: white;
}

.mobiscroll-eventcalendar .mbsc-event[data-resource="training"] {
    background: #f57c00;
    color: white;
}

.mobiscroll-eventcalendar .mbsc-event[data-resource="review"] {
    background: #7b1fa2;
    color: white;
}

.mobiscroll-eventcalendar .mbsc-event[data-resource="other"] {
    background: #616161;
    color: white;
}

/* ===== EVENT LIST CONTAINER ===== */
.event-list-container {
    background: #ffffff;
    border-radius: var(--mobiscroll-radius);
    box-shadow: var(--mobiscroll-shadow);
    border: 1px solid var(--mobiscroll-border);
    padding: 1.5rem;
}

.event-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.event-item {
    background: #ffffff;
    border: 1px solid var(--mobiscroll-border);
    border-radius: var(--mobiscroll-radius);
    padding: 1.5rem;
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.event-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--mobiscroll-primary);
    transition: width 0.2s ease-in-out;
}

.event-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: var(--mobiscroll-primary);
}

.event-item:hover::before {
    width: 6px;
}

.event-details h4 {
    color: var(--mobiscroll-text);
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 1.125rem;
}

.event-details p {
    color: #666666;
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.event-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.event-meta span {
    color: #666666;
    font-size: 0.875rem;
}

/* ===== PRIORITY INDICATORS ===== */
.priority-high {
    background: #ffebee;
    color: #d32f2f;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-left: auto;
    border: 1px solid #d32f2f;
}

.priority-medium {
    background: #fff8e1;
    color: #f57c00;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-left: auto;
    border: 1px solid #f57c00;
}

.priority-low {
    background: #e8f5e8;
    color: #388e3c;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-left: auto;
    border: 1px solid #388e3c;
}

/* ===== MODAL STYLING ===== */
.slds-modal__container {
    border-radius: var(--mobiscroll-radius);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    border: none;
}

.slds-modal__header {
    background: var(--mobiscroll-primary);
    color: white;
    border-radius: var(--mobiscroll-radius) var(--mobiscroll-radius) 0 0;
    padding: 1.5rem;
}

.slds-modal__header h2 {
    color: white;
    font-weight: 600;
}

.slds-modal__header .slds-button_icon-inverse {
    color: white;
}

.slds-modal__content {
    padding: 2rem;
}

.slds-modal__footer {
    background: #f8f9fa;
    border-top: 1px solid var(--mobiscroll-border);
    padding: 1.5rem;
    border-radius: 0 0 var(--mobiscroll-radius) var(--mobiscroll-radius);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .mobiscroll-eventcalendar {
        min-height: 400px;
    }

    .event-list-container {
        padding: 1rem;
    }

    .event-item {
        padding: 1rem;
    }

    .event-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .slds-modal__content {
        padding: 1rem;
    }

    .slds-modal__header,
    .slds-modal__footer {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .mobiscroll-eventcalendar {
        min-height: 350px;
    }

    .event-list-container {
        padding: 0.75rem;
    }

    .event-item {
        padding: 0.75rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobiscroll-calendar-container {
    animation: fadeInUp 0.3s ease-out;
}

.event-list-container {
    animation: fadeInUp 0.3s ease-out;
}

.event-item {
    animation: fadeInUp 0.3s ease-out;
}

.event-item:nth-child(2) {
    animation-delay: 0.1s;
}

.event-item:nth-child(3) {
    animation-delay: 0.2s;
}

.event-item:nth-child(4) {
    animation-delay: 0.3s;
}

.event-item:nth-child(5) {
    animation-delay: 0.4s;
}

/* ===== ACCESSIBILITY ===== */
.mobiscroll-eventcalendar:focus {
    outline: 2px solid var(--mobiscroll-primary);
    outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .event-actions {
        display: none;
    }

    .mobiscroll-calendar-container,
    .event-list-container {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}