/* ===== MATERIAL DESIGN VARIABLES ===== */
:host {
    /* Material Design 3 Color Palette */
    --md-primary: #6750a4;
    --md-primary-container: #eaddff;
    --md-on-primary: #ffffff;
    --md-on-primary-container: #21005d;
    --md-secondary: #625b71;
    --md-secondary-container: #e8def8;
    --md-on-secondary: #ffffff;
    --md-on-secondary-container: #1d192b;
    --md-tertiary: #7d5260;
    --md-tertiary-container: #ffd8e4;
    --md-on-tertiary: #ffffff;
    --md-on-tertiary-container: #31111d;
    --md-surface: #fffbfe;
    --md-surface-variant: #f4eff4;
    --md-on-surface: #1c1b1f;
    --md-on-surface-variant: #49454f;
    --md-outline: #79747e;
    --md-outline-variant: #cab6cf;
    --md-error: #ba1a1a;
    --md-error-container: #ffdad6;
    --md-on-error: #ffffff;
    --md-on-error-container: #410002;
    --md-success: #006e1c;
    --md-success-container: #9ef7b0;
    --md-warning: #8c5000;
    --md-warning-container: #ffdcc0;

    /* Material Design Elevation Shadows */
    --md-elevation-1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
    --md-elevation-2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
    --md-elevation-3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
    --md-elevation-4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
    --md-elevation-5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);

    /* Material Design Typography Scale */
    --md-display-large: 400 57px/64px 'Google Sans', 'Roboto', sans-serif;
    --md-display-medium: 400 45px/52px 'Google Sans', 'Roboto', sans-serif;
    --md-display-small: 400 36px/44px 'Google Sans', 'Roboto', sans-serif;
    --md-headline-large: 400 32px/40px 'Google Sans', 'Roboto', sans-serif;
    --md-headline-medium: 400 28px/36px 'Google Sans', 'Roboto', sans-serif;
    --md-headline-small: 400 24px/32px 'Google Sans', 'Roboto', sans-serif;
    --md-title-large: 400 22px/28px 'Roboto', sans-serif;
    --md-title-medium: 500 16px/24px 'Roboto', sans-serif;
    --md-title-small: 500 14px/20px 'Roboto', sans-serif;
    --md-body-large: 400 16px/24px 'Roboto', sans-serif;
    --md-body-medium: 400 14px/20px 'Roboto', sans-serif;
    --md-body-small: 400 12px/16px 'Roboto', sans-serif;
    --md-label-large: 500 14px/20px 'Roboto', sans-serif;
    --md-label-medium: 500 12px/16px 'Roboto', sans-serif;
    --md-label-small: 500 11px/16px 'Roboto', sans-serif;

    /* Material Design Shape */
    --md-shape-corner-none: 0px;
    --md-shape-corner-extra-small: 4px;
    --md-shape-corner-small: 8px;
    --md-shape-corner-medium: 12px;
    --md-shape-corner-large: 16px;
    --md-shape-corner-extra-large: 28px;

    /* Material Design Motion */
    --md-motion-duration-short1: 50ms;
    --md-motion-duration-short2: 100ms;
    --md-motion-duration-short3: 150ms;
    --md-motion-duration-short4: 200ms;
    --md-motion-duration-medium1: 250ms;
    --md-motion-duration-medium2: 300ms;
    --md-motion-duration-medium3: 350ms;
    --md-motion-duration-medium4: 400ms;
    --md-motion-duration-long1: 450ms;
    --md-motion-duration-long2: 500ms;
    --md-motion-duration-long3: 550ms;
    --md-motion-duration-long4: 600ms;
    --md-motion-easing-linear: cubic-bezier(0, 0, 1, 1);
    --md-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
    --md-motion-easing-standard-accelerate: cubic-bezier(0.3, 0, 1, 1);
    --md-motion-easing-standard-decelerate: cubic-bezier(0, 0, 0, 1);
    --md-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
    --md-motion-easing-emphasized-accelerate: cubic-bezier(0.3, 0, 0.8, 0.15);
    --md-motion-easing-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* ===== MATERIAL DESIGN CALENDAR NAVIGATION ===== */
.calendar-navigation {
    background: var(--md-surface);
    border-radius: var(--md-shape-corner-large);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--md-elevation-2);
    border: 1px solid var(--md-outline-variant);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: box-shadow var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.calendar-navigation:hover {
    box-shadow: var(--md-elevation-3);
}

.calendar-navigation h2 {
    font: var(--md-headline-small);
    color: var(--md-on-surface);
    margin: 0;
    letter-spacing: 0;
    user-select: none;
}

.view-toggle {
    display: flex;
    gap: 8px;
}

/* Material Design Button Styling */
.calendar-navigation lightning-button-icon {
    --slds-c-button-radius-border: var(--md-shape-corner-extra-large);
    --slds-c-button-color-background: var(--md-surface-variant);
    --slds-c-button-color-border: var(--md-outline);
    transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.calendar-navigation lightning-button-icon:hover {
    --slds-c-button-color-background: var(--md-secondary-container);
    transform: scale(1.05);
}

.view-toggle lightning-button {
    --slds-c-button-radius-border: var(--md-shape-corner-extra-large);
    --slds-c-button-color-background: var(--md-surface-variant);
    --slds-c-button-color-border: var(--md-outline);
    --slds-c-button-text-color: var(--md-on-surface-variant);
    font: var(--md-label-large);
    transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.view-toggle lightning-button[variant="brand"] {
    --slds-c-button-color-background: var(--md-primary);
    --slds-c-button-text-color: var(--md-on-primary);
    --slds-c-button-color-border: var(--md-primary);
    box-shadow: var(--md-elevation-1);
}

/* ===== MATERIAL DESIGN CALENDAR CONTAINER ===== */
.calendar-container {
    background: var(--md-surface);
    border-radius: var(--md-shape-corner-large);
    box-shadow: var(--md-elevation-2);
    border: 1px solid var(--md-outline-variant);
    overflow: hidden;
    transition: box-shadow var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.calendar-container:hover {
    box-shadow: var(--md-elevation-3);
}

/* ===== MATERIAL DESIGN CALENDAR HEADER ===== */
.calendar-header {
    background: var(--md-primary-container);
    color: var(--md-on-primary-container);
    padding: 0;
    margin: 0;
    position: relative;
}

.calendar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-tertiary) 100%);
    opacity: 0.1;
    pointer-events: none;
}

.calendar-day-header {
    flex: 1;
    padding: 20px 8px;
    font: var(--md-label-large);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    text-align: center;
    border-right: 1px solid var(--md-outline-variant);
    position: relative;
    background: var(--md-surface);
    color: var(--md-on-surface-variant);
    user-select: none;
}

.calendar-day-header:last-child {
    border-right: none;
}

.calendar-day-header:first-child,
.calendar-day-header:last-child {
    color: var(--md-error);
    font-weight: 600;
}

/* ===== MATERIAL DESIGN CALENDAR BODY ===== */
.calendar-body {
    background: var(--md-surface);
}

.calendar-week {
    border-bottom: 1px solid var(--md-outline-variant);
    display: flex;
}

.calendar-week:last-child {
    border-bottom: none;
}

/* ===== MATERIAL DESIGN CALENDAR DAY CELLS ===== */
.calendar-day {
    flex: 1;
    min-height: 120px;
    padding: 16px;
    border-right: 1px solid var(--md-outline-variant);
    cursor: pointer;
    transition: all var(--md-motion-duration-medium2) var(--md-motion-easing-emphasized);
    position: relative;
    background: var(--md-surface);
    display: flex;
    flex-direction: column;
    user-select: none;
}

.calendar-day:last-child {
    border-right: none;
}

.calendar-day::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--md-primary);
    opacity: 0;
    transition: opacity var(--md-motion-duration-short4) var(--md-motion-easing-standard);
    pointer-events: none;
}

.calendar-day:hover::before {
    opacity: 0.08;
}

.calendar-day:hover {
    box-shadow: var(--md-elevation-1);
    transform: translateY(-2px);
}

.calendar-day.other-month {
    background: var(--md-surface-variant);
    color: var(--md-on-surface-variant);
    opacity: 0.6;
}

.calendar-day.other-month:hover::before {
    background: var(--md-on-surface-variant);
    opacity: 0.04;
}

.calendar-day.today {
    background: var(--md-primary-container);
    color: var(--md-on-primary-container);
    border: 2px solid var(--md-primary);
    box-shadow: var(--md-elevation-2);
}

.calendar-day.today::before {
    background: var(--md-primary);
    opacity: 0.12;
}

.calendar-day.today .day-number {
    color: var(--md-primary);
    font-weight: 600;
}

.calendar-day.selected {
    background: var(--md-primary);
    color: var(--md-on-primary);
    box-shadow: var(--md-elevation-3);
    transform: translateY(-4px);
}

.calendar-day.selected::before {
    opacity: 0;
}

.calendar-day.selected .day-number {
    color: var(--md-on-primary);
    font-weight: 600;
}

/* Material Design Ripple Effect */
.calendar-day:active {
    transform: translateY(0);
    transition: transform var(--md-motion-duration-short1) var(--md-motion-easing-standard);
}

.calendar-day:active::before {
    opacity: 0.16;
    transition: opacity var(--md-motion-duration-short1) var(--md-motion-easing-standard);
}

/* ===== MATERIAL DESIGN DAY NUMBER ===== */
.day-number {
    font: var(--md-title-medium);
    color: var(--md-on-surface);
    margin-bottom: 12px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--md-shape-corner-extra-large);
    transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

/* ===== MATERIAL DESIGN DAY EVENTS ===== */
.day-events {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: auto;
}

.event-dot {
    padding: 8px 12px;
    border-radius: var(--md-shape-corner-extra-large);
    font: var(--md-label-small);
    cursor: pointer;
    transition: all var(--md-motion-duration-medium2) var(--md-motion-easing-emphasized);
    border: none;
    text-align: left;
    line-height: 1.4;
    position: relative;
    overflow: hidden;
    box-shadow: var(--md-elevation-1);
}

.event-dot::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: currentColor;
    opacity: 0;
    transition: opacity var(--md-motion-duration-short4) var(--md-motion-easing-standard);
    pointer-events: none;
}

.event-dot:hover {
    transform: translateY(-2px);
    box-shadow: var(--md-elevation-3);
}

.event-dot:hover::before {
    opacity: 0.08;
}

.event-dot:active {
    transform: translateY(0);
    transition: transform var(--md-motion-duration-short1) var(--md-motion-easing-standard);
}

.event-dot:active::before {
    opacity: 0.16;
    transition: opacity var(--md-motion-duration-short1) var(--md-motion-easing-standard);
}

.event-title {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    font-weight: 500;
}

/* ===== MATERIAL DESIGN EVENT TYPE COLORS ===== */
.event-meeting {
    background: #1976d2;
    color: #ffffff;
}

.event-deadline {
    background: var(--md-error);
    color: var(--md-on-error);
}

.event-presentation {
    background: var(--md-success);
    color: #ffffff;
}

.event-training {
    background: var(--md-warning);
    color: #000000;
}

.event-review {
    background: var(--md-tertiary);
    color: var(--md-on-tertiary);
}

.event-other {
    background: var(--md-secondary);
    color: var(--md-on-secondary);
}

/* ===== MATERIAL DESIGN LIST VIEW ===== */
.list-container {
    background: var(--md-surface);
    border-radius: var(--md-shape-corner-large);
    box-shadow: var(--md-elevation-2);
    border: 1px solid var(--md-outline-variant);
    padding: 24px;
    transition: box-shadow var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.list-container:hover {
    box-shadow: var(--md-elevation-3);
}

.event-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.event-item {
    background: var(--md-surface);
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--md-shape-corner-medium);
    padding: 20px;
    transition: all var(--md-motion-duration-medium2) var(--md-motion-easing-emphasized);
    position: relative;
    overflow: hidden;
    box-shadow: var(--md-elevation-1);
}

.event-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--md-primary);
    transition: all var(--md-motion-duration-short4) var(--md-motion-easing-standard);
}

.event-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--md-primary);
    opacity: 0;
    transition: opacity var(--md-motion-duration-short4) var(--md-motion-easing-standard);
    pointer-events: none;
}

.event-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--md-elevation-4);
    border-color: var(--md-primary);
}

.event-item:hover::before {
    width: 8px;
}

.event-item:hover::after {
    opacity: 0.04;
}

.event-details h3 {
    font: var(--md-title-large);
    color: var(--md-on-surface);
    margin-bottom: 8px;
}

.event-details p {
    font: var(--md-body-medium);
    color: var(--md-on-surface-variant);
    margin-bottom: 16px;
    line-height: 1.5;
}

.event-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.event-meta span {
    font: var(--md-body-small);
    color: var(--md-on-surface-variant);
}

/* ===== MATERIAL DESIGN PRIORITY INDICATORS ===== */
.priority-high {
    background: var(--md-error-container);
    color: var(--md-on-error-container);
    padding: 4px 12px;
    border-radius: var(--md-shape-corner-extra-large);
    font: var(--md-label-small);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-left: auto;
    border: 1px solid var(--md-error);
}

.priority-medium {
    background: var(--md-warning-container);
    color: #000000;
    padding: 4px 12px;
    border-radius: var(--md-shape-corner-extra-large);
    font: var(--md-label-small);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-left: auto;
    border: 1px solid var(--md-warning);
}

.priority-low {
    background: var(--md-success-container);
    color: #000000;
    padding: 4px 12px;
    border-radius: var(--md-shape-corner-extra-large);
    font: var(--md-label-small);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-left: auto;
    border: 1px solid var(--md-success);
}

/* ===== MATERIAL DESIGN EMPTY STATE ===== */
.list-container .slds-text-align_center {
    padding: 48px 24px;
    background: var(--md-surface-variant);
    border-radius: var(--md-shape-corner-medium);
    border: 2px dashed var(--md-outline);
}

.list-container .slds-text-align_center lightning-icon {
    opacity: 0.6;
    margin-bottom: 16px;
    color: var(--md-on-surface-variant);
}

.list-container .slds-text-align_center h3 {
    font: var(--md-title-medium);
    color: var(--md-on-surface);
    margin-bottom: 8px;
}

.list-container .slds-text-align_center p {
    font: var(--md-body-medium);
    color: var(--md-on-surface-variant);
    max-width: 300px;
    margin: 0 auto;
    line-height: 1.5;
}

/* ===== MATERIAL DESIGN MODAL ===== */
.slds-modal__container {
    border-radius: var(--md-shape-corner-extra-large);
    box-shadow: var(--md-elevation-5);
    border: none;
    background: var(--md-surface);
}

.slds-modal__header {
    background: var(--md-primary-container);
    color: var(--md-on-primary-container);
    border-radius: var(--md-shape-corner-extra-large) var(--md-shape-corner-extra-large) 0 0;
    padding: 24px;
    position: relative;
}

.slds-modal__header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-tertiary) 100%);
    opacity: 0.1;
    pointer-events: none;
    border-radius: var(--md-shape-corner-extra-large) var(--md-shape-corner-extra-large) 0 0;
}

.slds-modal__header h2 {
    font: var(--md-headline-small);
    color: var(--md-on-primary-container);
    position: relative;
    z-index: 1;
}

.slds-modal__header .slds-button_icon-inverse {
    color: var(--md-on-primary-container);
    position: relative;
    z-index: 1;
}

.slds-modal__content {
    padding: 24px;
    background: var(--md-surface);
}

.slds-modal__footer {
    background: var(--md-surface-variant);
    border-top: 1px solid var(--md-outline-variant);
    padding: 24px;
    border-radius: 0 0 var(--md-shape-corner-extra-large) var(--md-shape-corner-extra-large);
}

/* ===== MATERIAL DESIGN RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .calendar-navigation {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        padding: 16px;
    }

    .calendar-day {
        min-height: 80px;
        padding: 12px;
    }

    .day-number {
        font: var(--md-body-large);
        width: 28px;
        height: 28px;
    }

    .event-dot {
        font: var(--md-label-small);
        padding: 4px 8px;
    }

    .event-item {
        padding: 16px;
    }

    .event-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .slds-modal__content {
        padding: 16px;
    }

    .slds-modal__header,
    .slds-modal__footer {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .calendar-day {
        min-height: 60px;
        padding: 8px;
    }

    .calendar-day-header {
        padding: 12px 4px;
        font: var(--md-label-medium);
    }

    .day-number {
        font: var(--md-body-medium);
        width: 24px;
        height: 24px;
        margin-bottom: 4px;
    }

    .event-dot {
        font: var(--md-label-small);
        padding: 2px 6px;
        border-radius: var(--md-shape-corner-small);
    }

    .calendar-navigation {
        padding: 12px;
    }

    .list-container {
        padding: 16px;
    }
}

/* ===== MATERIAL DESIGN ANIMATIONS ===== */
@keyframes md-fade-in {
    from {
        opacity: 0;
        transform: translateY(16px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes md-slide-in-right {
    from {
        opacity: 0;
        transform: translateX(24px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes md-scale-in {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes md-ripple {
    0% {
        transform: scale(0);
        opacity: 0.6;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Apply Material Design animations */
.calendar-container {
    animation: md-fade-in var(--md-motion-duration-medium4) var(--md-motion-easing-emphasized-decelerate);
}

.list-container {
    animation: md-fade-in var(--md-motion-duration-medium4) var(--md-motion-easing-emphasized-decelerate);
}

.event-item {
    animation: md-slide-in-right var(--md-motion-duration-medium3) var(--md-motion-easing-emphasized-decelerate);
}

.event-item:nth-child(2) {
    animation-delay: var(--md-motion-duration-short2);
}

.event-item:nth-child(3) {
    animation-delay: var(--md-motion-duration-short4);
}

.event-item:nth-child(4) {
    animation-delay: var(--md-motion-duration-medium1);
}

.event-item:nth-child(5) {
    animation-delay: var(--md-motion-duration-medium2);
}

.calendar-navigation {
    animation: md-scale-in var(--md-motion-duration-medium2) var(--md-motion-easing-emphasized-decelerate);
}

/* ===== MATERIAL DESIGN ACCESSIBILITY ===== */
.calendar-day:focus {
    outline: 2px solid var(--md-primary);
    outline-offset: 2px;
    box-shadow: var(--md-elevation-2);
}

.event-dot:focus {
    outline: 2px solid var(--md-primary);
    outline-offset: 2px;
    box-shadow: var(--md-elevation-3);
}

/* Focus indicators for buttons */
.calendar-navigation lightning-button:focus,
.calendar-navigation lightning-button-icon:focus {
    outline: 2px solid var(--md-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :host {
        --md-outline: #000000;
        --md-outline-variant: #666666;
    }

    .calendar-day {
        border-width: 2px;
    }

    .event-dot {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .calendar-day:hover {
        transform: none;
    }

    .event-dot:hover {
        transform: none;
    }

    .event-item:hover {
        transform: none;
    }
}

/* ===== MATERIAL DESIGN PRINT STYLES ===== */
@media print {
    .calendar-navigation,
    .view-toggle,
    .event-actions {
        display: none;
    }

    .calendar-container,
    .list-container {
        box-shadow: none;
        border: 1px solid #000;
        background: white;
    }

    .event-dot {
        background: white !important;
        color: #000 !important;
        border: 1px solid #000;
    }

    .calendar-day {
        background: white;
        color: #000;
    }

    .calendar-day.today {
        background: #f0f0f0;
        border: 2px solid #000;
    }
}