/* ===== CSS CUSTOM PROPERTIES ===== */
:host {
    --calendar-primary: #0176d3;
    --calendar-primary-light: #1b96ff;
    --calendar-primary-dark: #014486;
    --calendar-secondary: #f3f3f3;
    --calendar-accent: #ff6b35;
    --calendar-success: #04844b;
    --calendar-warning: #ffb75d;
    --calendar-error: #ea001e;
    --calendar-text-primary: #181818;
    --calendar-text-secondary: #706e6b;
    --calendar-text-muted: #a8a8a8;
    --calendar-border: #dddbda;
    --calendar-border-light: #f3f2f2;
    --calendar-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --calendar-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
    --calendar-radius: 8px;
    --calendar-radius-small: 4px;
    --calendar-transition: all 0.2s ease-in-out;
}

/* ===== CALENDAR NAVIGATION ===== */
.calendar-navigation {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: var(--calendar-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--calendar-shadow);
    border: 1px solid var(--calendar-border-light);
}

.calendar-navigation h2 {
    font-weight: 600;
    color: var(--calendar-text-primary);
    margin: 0;
    font-size: 1.25rem;
    letter-spacing: -0.025em;
}

.view-toggle lightning-button-group {
    border-radius: var(--calendar-radius-small);
    overflow: hidden;
}

/* ===== CALENDAR CONTAINER ===== */
.calendar-container {
    background: #ffffff;
    border-radius: var(--calendar-radius);
    box-shadow: var(--calendar-shadow);
    border: 1px solid var(--calendar-border-light);
    overflow: hidden;
}

/* ===== CALENDAR HEADER ===== */
.calendar-header {
    background: linear-gradient(135deg, var(--calendar-primary) 0%, var(--calendar-primary-light) 100%);
    color: white;
    padding: 0;
    margin: 0;
}

.calendar-day-header {
    flex: 1;
    padding: 1rem 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.calendar-day-header:last-child {
    border-right: none;
}

/* ===== CALENDAR BODY ===== */
.calendar-body {
    background: #ffffff;
}

.calendar-week {
    border-bottom: 1px solid var(--calendar-border-light);
}

.calendar-week:last-child {
    border-bottom: none;
}

/* ===== CALENDAR DAY CELLS ===== */
.calendar-day {
    flex: 1;
    min-height: 120px;
    padding: 0.75rem;
    border-right: 1px solid var(--calendar-border-light);
    cursor: pointer;
    transition: var(--calendar-transition);
    position: relative;
    background: #ffffff;
    display: flex;
    flex-direction: column;
}

.calendar-day:last-child {
    border-right: none;
}

.calendar-day:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    transform: translateY(-1px);
    box-shadow: inset 0 0 0 1px var(--calendar-primary-light);
}

.calendar-day.other-month {
    background: #fafafa;
    color: var(--calendar-text-muted);
}

.calendar-day.other-month:hover {
    background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
}

.calendar-day.today {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3f9ff 100%);
    border: 2px solid var(--calendar-primary);
    box-shadow: 0 0 0 1px var(--calendar-primary-light);
}

.calendar-day.today .day-number {
    color: var(--calendar-primary);
    font-weight: 700;
}

.calendar-day.selected {
    background: linear-gradient(135deg, var(--calendar-primary-light) 0%, var(--calendar-primary) 100%);
    color: white;
    box-shadow: var(--calendar-shadow-hover);
}

.calendar-day.selected .day-number {
    color: white;
    font-weight: 700;
}

/* ===== DAY NUMBER ===== */
.day-number {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--calendar-text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

/* ===== DAY EVENTS ===== */
.day-events {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-top: auto;
}

.event-dot {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--calendar-transition);
    border: 1px solid transparent;
    text-align: center;
    line-height: 1.2;
}

.event-dot:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.event-title {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* ===== EVENT TYPE COLORS ===== */
.event-meeting {
    background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
    color: white;
}

.event-meeting:hover {
    background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
}

.event-deadline {
    background: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);
    color: white;
}

.event-deadline:hover {
    background: linear-gradient(135deg, #d33b2c 0%, #b52d20 100%);
}

.event-presentation {
    background: linear-gradient(135deg, #34a853 0%, #2d8f47 100%);
    color: white;
}

.event-presentation:hover {
    background: linear-gradient(135deg, #2d8f47 0%, #1e5f31 100%);
}

.event-training {
    background: linear-gradient(135deg, #fbbc04 0%, #f9ab00 100%);
    color: #1f1f1f;
}

.event-training:hover {
    background: linear-gradient(135deg, #f9ab00 0%, #e8a317 100%);
}

.event-review {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    color: white;
}

.event-review:hover {
    background: linear-gradient(135deg, #7b1fa2 0%, #4a148c 100%);
}

.event-other {
    background: linear-gradient(135deg, #607d8b 0%, #455a64 100%);
    color: white;
}

.event-other:hover {
    background: linear-gradient(135deg, #455a64 0%, #263238 100%);
}

/* ===== LIST VIEW ===== */
.list-container {
    background: #ffffff;
    border-radius: var(--calendar-radius);
    box-shadow: var(--calendar-shadow);
    border: 1px solid var(--calendar-border-light);
    padding: 1.5rem;
}

.event-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.event-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--calendar-border-light);
    border-radius: var(--calendar-radius);
    padding: 1.5rem;
    transition: var(--calendar-transition);
    position: relative;
    overflow: hidden;
}

.event-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--calendar-primary);
    transition: var(--calendar-transition);
}

.event-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--calendar-shadow-hover);
    border-color: var(--calendar-primary-light);
}

.event-item:hover::before {
    width: 6px;
}

.event-details h3 {
    color: var(--calendar-text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 1.125rem;
}

.event-details p {
    color: var(--calendar-text-secondary);
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.event-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.event-meta span {
    color: var(--calendar-text-secondary);
    font-size: 0.875rem;
}

/* ===== PRIORITY INDICATORS ===== */
.priority-high {
    background: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-left: auto;
}

.priority-medium {
    background: linear-gradient(135deg, #fbbc04 0%, #f9ab00 100%);
    color: #1f1f1f;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-left: auto;
}

.priority-low {
    background: linear-gradient(135deg, #34a853 0%, #2d8f47 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-left: auto;
}

/* ===== EMPTY STATE ===== */
.list-container .slds-text-align_center {
    padding: 3rem 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: var(--calendar-radius);
    border: 2px dashed var(--calendar-border);
}

.list-container .slds-text-align_center lightning-icon {
    opacity: 0.6;
    margin-bottom: 1rem;
}

.list-container .slds-text-align_center h3 {
    color: var(--calendar-text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.list-container .slds-text-align_center p {
    color: var(--calendar-text-secondary);
    max-width: 300px;
    margin: 0 auto;
    line-height: 1.5;
}

/* ===== MODAL ENHANCEMENTS ===== */
.slds-modal__container {
    border-radius: var(--calendar-radius);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    border: none;
}

.slds-modal__header {
    background: linear-gradient(135deg, var(--calendar-primary) 0%, var(--calendar-primary-light) 100%);
    color: white;
    border-radius: var(--calendar-radius) var(--calendar-radius) 0 0;
    padding: 1.5rem;
}

.slds-modal__header h2 {
    color: white;
    font-weight: 600;
}

.slds-modal__header .slds-button_icon-inverse {
    color: white;
}

.slds-modal__content {
    padding: 2rem;
}

.slds-modal__footer {
    background: #f8f9fa;
    border-top: 1px solid var(--calendar-border-light);
    padding: 1.5rem;
    border-radius: 0 0 var(--calendar-radius) var(--calendar-radius);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .calendar-navigation {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .calendar-day {
        min-height: 80px;
        padding: 0.5rem;
    }

    .day-number {
        font-size: 1rem;
    }

    .event-dot {
        font-size: 0.625rem;
        padding: 0.125rem 0.25rem;
    }

    .event-item {
        padding: 1rem;
    }

    .event-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .calendar-day {
        min-height: 60px;
        padding: 0.25rem;
    }

    .calendar-day-header {
        padding: 0.75rem 0.25rem;
        font-size: 0.75rem;
    }

    .day-number {
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }

    .event-dot {
        font-size: 0.5rem;
        padding: 0.125rem;
        border-radius: 8px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.calendar-container {
    animation: fadeInUp 0.3s ease-out;
}

.list-container {
    animation: fadeInUp 0.3s ease-out;
}

.event-item {
    animation: slideInRight 0.3s ease-out;
}

.event-item:nth-child(2) {
    animation-delay: 0.1s;
}

.event-item:nth-child(3) {
    animation-delay: 0.2s;
}

.event-item:nth-child(4) {
    animation-delay: 0.3s;
}

.event-item:nth-child(5) {
    animation-delay: 0.4s;
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
.calendar-day:focus {
    outline: 2px solid var(--calendar-primary);
    outline-offset: 2px;
}

.event-dot:focus {
    outline: 2px solid var(--calendar-primary);
    outline-offset: 1px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .calendar-navigation,
    .view-toggle,
    .event-actions {
        display: none;
    }

    .calendar-container,
    .list-container {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .event-dot {
        background: #f0f0f0 !important;
        color: #000 !important;
        border: 1px solid #ccc;
    }
}