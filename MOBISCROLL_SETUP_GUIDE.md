# Mobiscroll Static Resource Setup Guide

## 📋 Prerequisites
- Mobiscroll library files (CSS and JS)
- Salesforce org with Lightning Web Components enabled

## 🚀 Step-by-Step Setup

### 1. Prepare Mobiscroll Files
Extract your Mobiscroll zip file and locate these files:
- `mobiscroll.min.css` (or `mobiscroll.javascript.min.css`)
- `mobiscroll.min.js` (or `mobiscroll.javascript.min.js`)

### 2. Create Folder Structure
Create a folder structure like this:
```
mobiscroll/
├── css/
│   └── mobiscroll.min.css
└── js/
    └── mobiscroll.min.js
```

### 3. Create ZIP File
- Zip the `mobiscroll` folder (not the contents, but the folder itself)
- The zip should contain the folder structure above

### 4. Upload to Salesforce
1. Go to **Setup** → **Static Resources**
2. Click **New**
3. Fill in:
   - **Name**: `mobiscroll`
   - **File**: Upload your zip file
   - **Cache Control**: Public
4. Click **Save**

### 5. Verify Upload
The static resource should be accessible at:
- CSS: `/resource/mobiscroll/css/mobiscroll.min.css`
- JS: `/resource/mobiscroll/js/mobiscroll.min.js`

## 🔍 Troubleshooting

### Common Issues:

#### 1. "Failed to load calendar library" Error
**Possible Causes:**
- Static resource not deployed
- Incorrect folder structure
- Wrong file names
- Static resource name mismatch

**Solutions:**
- Check browser console for detailed error messages
- Verify static resource exists in Setup → Static Resources
- Ensure folder structure matches exactly
- Try the "Retry Loading" button

#### 2. Files Not Found (404 Error)
**Check:**
- Folder structure: `css/` and `js/` folders exist
- File names: exactly `mobiscroll.min.css` and `mobiscroll.min.js`
- Static resource name: exactly `mobiscroll`

#### 3. JavaScript Errors
**Check:**
- Mobiscroll files are valid and not corrupted
- Files are the correct version (JavaScript, not jQuery version)
- No conflicting JavaScript libraries

### Alternative File Structures
The component will try these paths automatically:
1. `/css/mobiscroll.javascript.min.css` and `/js/mobiscroll.javascript.min.js`
2. `/css/mobiscroll.min.css` and `/js/mobiscroll.min.js`
3. `/mobiscroll.min.css` and `/mobiscroll.min.js` (root level)
4. `/mobiscroll.javascript.min.css` and `/mobiscroll.javascript.min.js` (root level)
5. `/mobiscroll.css` and `/mobiscroll.js`
6. `/dist/css/mobiscroll.min.css` and `/dist/js/mobiscroll.min.js`

## 🔧 Debug Information

### Check Browser Console
Open browser developer tools and look for:
- Loading messages: "Loading Mobiscroll from: ..."
- Path attempts: "Trying CSS path: ..." and "Trying JS path: ..."
- Success messages: "CSS loaded successfully", "JS loaded successfully"
- Error messages with specific details

### Verify Static Resource
1. Go to Setup → Static Resources
2. Find "mobiscroll" resource
3. Click "View File" to verify contents
4. Ensure the zip contains the correct folder structure

## 📞 Support
If you continue to have issues:
1. Check the browser console for specific error messages
2. Verify the Mobiscroll files are valid
3. Try uploading with a different folder structure
4. Use the "Retry Loading" button after making changes
