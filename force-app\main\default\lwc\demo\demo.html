<template>
    <lightning-card title="Mobiscroll Event Calendar" icon-name="standard:event">
        <div slot="actions">
            <lightning-button
                label="Add Event"
                variant="brand"
                onclick={handleAddEvent}
                icon-name="utility:add">
            </lightning-button>
            <lightning-button
                label="Today"
                variant="neutral"
                onclick={handleGoToToday}>
            </lightning-button>
        </div>

        <div class="slds-p-around_medium">
            <!-- Mobiscroll Calendar Container -->
            <div class="mobiscroll-calendar-container">
                <div lwc:dom="manual" class="mobiscroll-eventcalendar"></div>
            </div>

            <!-- Event List View (Optional) -->
            <template if:true={showEventList}>
                <div class="event-list-container slds-m-top_large">
                    <div class="slds-grid slds-grid_align-spread slds-m-bottom_medium">
                        <h3 class="slds-text-heading_medium">Upcoming Events</h3>
                        <lightning-button
                            label={eventListToggleLabel}
                            variant="neutral"
                            onclick={handleToggleEventList}>
                        </lightning-button>
                    </div>

                    <template if:true={hasEvents}>
                        <div class="event-list">
                            <template for:each={sortedEvents} for:item="event">
                                <div key={event.id} class="event-item slds-box slds-m-bottom_small">
                                    <div class="slds-grid slds-grid_align-spread">
                                        <div class="event-details">
                                            <h4 class="slds-text-heading_small">{event.title}</h4>
                                            <p class="slds-text-body_small slds-text-color_weak">{event.description}</p>
                                            <div class="event-meta slds-grid slds-grid_vertical-align-center slds-m-top_x-small">
                                                <lightning-icon icon-name="utility:event" size="x-small" class="slds-m-right_x-small"></lightning-icon>
                                                <span class="slds-text-body_small">{event.formattedDateTime}</span>
                                                <span class={event.priorityClass}>{event.priority}</span>
                                            </div>
                                        </div>
                                        <div class="event-actions">
                                            <lightning-button-menu
                                                alternative-text="Event Actions"
                                                icon-size="x-small"
                                                menu-alignment="right">
                                                <lightning-menu-item
                                                    value="edit"
                                                    label="Edit"
                                                    onclick={handleEditEvent}
                                                    data-event-id={event.id}>
                                                </lightning-menu-item>
                                                <lightning-menu-item
                                                    value="delete"
                                                    label="Delete"
                                                    onclick={handleDeleteEvent}
                                                    data-event-id={event.id}>
                                                </lightning-menu-item>
                                            </lightning-button-menu>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>
                    <template if:false={hasEvents}>
                        <div class="slds-text-align_center slds-p-vertical_large">
                            <lightning-icon icon-name="utility:event" size="large" class="slds-m-bottom_small"></lightning-icon>
                            <h3 class="slds-text-heading_small">No events found</h3>
                            <p class="slds-text-body_regular slds-text-color_weak">Create your first event to get started.</p>
                        </div>
                    </template>
                </div>
            </template>
        </div>
    </lightning-card>

    <!-- Event Modal -->
    <template if:true={showEventModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                            title="Close" onclick={handleCloseModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-id_modal slds-modal__title slds-text-heading_medium">
                        {modalTitle}
                    </h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-record-edit-form object-api-name="Event" onsubmit={handleEventSubmit}>
                        <div class="slds-grid slds-gutters slds-wrap">
                            <div class="slds-col slds-size_1-of-1">
                                <lightning-input
                                    label="Event Title"
                                    name="title"
                                    value={currentEvent.title}
                                    onchange={handleInputChange}
                                    required>
                                </lightning-input>
                            </div>
                            <div class="slds-col slds-size_1-of-1">
                                <lightning-textarea
                                    label="Description"
                                    name="description"
                                    value={currentEvent.description}
                                    onchange={handleInputChange}>
                                </lightning-textarea>
                            </div>
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-input
                                    label="Start Date"
                                    type="date"
                                    name="startDate"
                                    value={currentEvent.startDate}
                                    onchange={handleInputChange}
                                    required>
                                </lightning-input>
                            </div>
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-input
                                    label="End Date"
                                    type="date"
                                    name="endDate"
                                    value={currentEvent.endDate}
                                    onchange={handleInputChange}
                                    required>
                                </lightning-input>
                            </div>
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-input
                                    label="Start Time"
                                    type="time"
                                    name="startTime"
                                    value={currentEvent.startTime}
                                    onchange={handleInputChange}>
                                </lightning-input>
                            </div>
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-input
                                    label="End Time"
                                    type="time"
                                    name="endTime"
                                    value={currentEvent.endTime}
                                    onchange={handleInputChange}>
                                </lightning-input>
                            </div>
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-combobox
                                    label="Event Type"
                                    name="type"
                                    value={currentEvent.type}
                                    options={eventTypeOptions}
                                    onchange={handleInputChange}>
                                </lightning-combobox>
                            </div>
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-combobox
                                    label="Priority"
                                    name="priority"
                                    value={currentEvent.priority}
                                    options={priorityOptions}
                                    onchange={handleInputChange}>
                                </lightning-combobox>
                            </div>
                        </div>
                    </lightning-record-edit-form>
                </div>
                <footer class="slds-modal__footer">
                    <lightning-button
                        label="Cancel"
                        onclick={handleCloseModal}>
                    </lightning-button>
                    <lightning-button
                        label={saveButtonLabel}
                        variant="brand"
                        onclick={handleSaveEvent}>
                    </lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>