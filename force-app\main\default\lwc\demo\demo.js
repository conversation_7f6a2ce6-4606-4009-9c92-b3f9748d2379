import { LightningElement, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { loadScript, loadStyle } from 'lightning/platformResourceLoader';
import MOBISCROLL_RESOURCE from '@salesforce/resourceUrl/mobiscroll';
const eventsData = [
    {
        "id": "event1",
        "title": "Team Meeting",
        "description": "Weekly team sync meeting",
        "startDate": "2025-06-23",
        "endDate": "2025-06-23",
        "startTime": "09:00",
        "endTime": "10:00",
        "type": "meeting",
        "priority": "high"
    },
    {
        "id": "event2",
        "title": "Project Deadline",
        "description": "Final submission for Q2 project",
        "startDate": "2025-06-25",
        "endDate": "2025-06-25",
        "startTime": "17:00",
        "endTime": "17:30",
        "type": "deadline",
        "priority": "high"
    },
    {
        "id": "event3",
        "title": "Client Presentation",
        "description": "Present new features to client",
        "startDate": "2025-06-27",
        "endDate": "2025-06-27",
        "startTime": "14:00",
        "endTime": "15:30",
        "type": "presentation",
        "priority": "medium"
    },
    {
        "id": "event4",
        "title": "Training Session",
        "description": "Salesforce Lightning Web Components training",
        "startDate": "2025-06-30",
        "endDate": "2025-06-30",
        "startTime": "10:00",
        "endTime": "12:00",
        "type": "training",
        "priority": "low"
    },
    {
        "id": "event5",
        "title": "Code Review",
        "description": "Review pull requests and code quality",
        "startDate": "2025-07-02",
        "endDate": "2025-07-02",
        "startTime": "11:00",
        "endTime": "12:00",
        "type": "review",
        "priority": "medium"
    }
];

export default class Demo extends LightningElement {
    @track events = [];
    @track showEventModal = false;
    @track currentEvent = {};
    @track isEditMode = false;
    @track showEventList = false;
    @track mobiscrollLoaded = false;

    // Mobiscroll calendar instance
    calendar = null;

    // Form options
    eventTypeOptions = [
        { label: 'Meeting', value: 'meeting' },
        { label: 'Deadline', value: 'deadline' },
        { label: 'Presentation', value: 'presentation' },
        { label: 'Training', value: 'training' },
        { label: 'Review', value: 'review' },
        { label: 'Other', value: 'other' }
    ];

    priorityOptions = [
        { label: 'High', value: 'high' },
        { label: 'Medium', value: 'medium' },
        { label: 'Low', value: 'low' }
    ];

    connectedCallback() {
        this.loadEvents();
        this.loadMobiscroll();
    }

    disconnectedCallback() {
        // Clean up Mobiscroll instance
        if (this.calendar) {
            this.calendar.destroy();
        }
    }

    // Load Mobiscroll library and initialize calendar
    async loadMobiscroll() {
        try {
            // Load CSS first
            await loadStyle(this, MOBISCROLL_RESOURCE + '/mobiscroll/css/mobiscroll.javascript.min.css');

            // Load JavaScript
            await loadScript(this, MOBISCROLL_RESOURCE + '/mobiscroll/js/mobiscroll.javascript.min.js');

            this.mobiscrollLoaded = true;
            this.initializeMobiscrollCalendar();
        } catch (error) {
            console.error('Error loading Mobiscroll:', error);
            this.showToast('Error', 'Failed to load calendar library', 'error');
        }
    }

    // Initialize Mobiscroll Event Calendar
    initializeMobiscrollCalendar() {
        if (!this.mobiscrollLoaded || !window.mobiscroll) {
            console.error('Mobiscroll not loaded');
            return;
        }

        const calendarElement = this.template.querySelector('.mobiscroll-eventcalendar');
        if (!calendarElement) {
            console.error('Calendar element not found');
            return;
        }

        // Convert events to Mobiscroll format
        const mobiscrollEvents = this.convertEventsToMobiscrollFormat(this.events);

        // Initialize Mobiscroll Event Calendar
        this.calendar = window.mobiscroll.Eventcalendar(calendarElement, {
            // Calendar configuration
            view: {
                calendar: {
                    type: 'month'
                },
                agenda: {
                    type: 'month'
                }
            },

            // Data
            data: mobiscrollEvents,

            // Styling
            theme: 'material',
            themeVariant: 'light',

            // Event handlers
            onEventClick: (event, inst) => {
                this.handleMobiscrollEventClick(event);
            },

            onCellClick: (event, inst) => {
                this.handleMobiscrollCellClick(event);
            },

            onEventCreate: (event, inst) => {
                this.handleMobiscrollEventCreate(event);
            },

            onEventUpdate: (event, inst) => {
                this.handleMobiscrollEventUpdate(event);
            },

            onEventDelete: (event, inst) => {
                this.handleMobiscrollEventDelete(event);
            },

            // Enable drag and drop
            dragToCreate: true,
            dragToMove: true,
            dragToResize: true,

            // Calendar settings
            calendarType: 'gregorian',
            firstDay: 0, // Sunday

            // Responsive settings
            responsive: {
                xsmall: {
                    view: {
                        calendar: { type: 'month' },
                        agenda: { type: 'day' }
                    }
                },
                small: {
                    view: {
                        calendar: { type: 'month' },
                        agenda: { type: 'week' }
                    }
                }
            }
        });
        console.log('Calendar initialized:', this.calendar);
    }

    // Convert events to Mobiscroll format
    convertEventsToMobiscrollFormat(events) {
        return events.map(event => {
            const startDateTime = event.startTime ?
                `${event.startDate}T${event.startTime}:00` :
                event.startDate;

            const endDateTime = event.endTime ?
                `${event.endDate}T${event.endTime}:00` :
                event.endDate;

            return {
                id: event.id,
                title: event.title,
                start: startDateTime,
                end: endDateTime,
                description: event.description,
                color: this.getEventColor(event.type),
                resource: event.type,
                // Store original event data for reference
                originalEvent: event
            };
        });
    }

    // Get color based on event type
    getEventColor(eventType) {
        const colorMap = {
            'meeting': '#1976d2',
            'deadline': '#d32f2f',
            'presentation': '#388e3c',
            'training': '#f57c00',
            'review': '#7b1fa2',
            'other': '#616161'
        };
        return colorMap[eventType] || colorMap.other;
    }

    // Initialize events with local data
    loadEvents() {
        try {
            // Use the imported local JSON data directly
            this.events = eventsData;

            // Update Mobiscroll calendar if it's already initialized
            if (this.calendar) {
                this.updateMobiscrollEvents();
            }
        } catch (error) {
            console.error('Error loading initial events:', error);
            this.events = []; // Fallback to empty array
        }
    }

    // Update Mobiscroll calendar with new events
    updateMobiscrollEvents() {
        if (this.calendar) {
            const mobiscrollEvents = this.convertEventsToMobiscrollFormat(this.events);
            this.calendar.setEvents(mobiscrollEvents);
        }
    }

    // Mobiscroll event handlers
    handleMobiscrollEventClick(event) {
        const originalEvent = event.event.originalEvent;
        if (originalEvent) {
            this.handleEditEventById(originalEvent.id);
        }
    }

    handleMobiscrollCellClick(event) {
        const selectedDate = new Date(event.date);
        const dateStr = this.formatDateToString(selectedDate);
        this.handleAddEvent(dateStr);
    }

    handleMobiscrollEventCreate(event) {
        // This will be called when user creates event via drag
        const newEvent = this.convertMobiscrollEventToLocal(event.event);
        this.events = [...this.events, newEvent];
        this.showToast('Success', 'Event created successfully', 'success');
    }

    handleMobiscrollEventUpdate(event) {
        // This will be called when user updates event via drag
        const updatedEvent = this.convertMobiscrollEventToLocal(event.event);
        const eventIndex = this.events.findIndex(e => e.id === updatedEvent.id);
        if (eventIndex !== -1) {
            this.events = [
                ...this.events.slice(0, eventIndex),
                updatedEvent,
                ...this.events.slice(eventIndex + 1)
            ];
            this.showToast('Success', 'Event updated successfully', 'success');
        }
    }

    handleMobiscrollEventDelete(event) {
        // This will be called when user deletes event
        const eventId = event.event.id;
        this.events = this.events.filter(e => e.id !== eventId);
        this.showToast('Success', 'Event deleted successfully', 'success');
    }

    // Convert Mobiscroll event back to local format
    convertMobiscrollEventToLocal(mobiscrollEvent) {
        const startDate = new Date(mobiscrollEvent.start);
        const endDate = new Date(mobiscrollEvent.end);

        return {
            id: mobiscrollEvent.id || this.generateId(),
            title: mobiscrollEvent.title,
            description: mobiscrollEvent.description || '',
            startDate: this.formatDateToString(startDate),
            endDate: this.formatDateToString(endDate),
            startTime: this.formatTimeFromDate(startDate),
            endTime: this.formatTimeFromDate(endDate),
            type: mobiscrollEvent.resource || 'other',
            priority: 'medium'
        };
    }

    // Format time from Date object
    formatTimeFromDate(date) {
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${hours}:${minutes}`;
    }

    // Computed properties
    get hasEvents() {
        return this.events && this.events.length > 0;
    }

    get eventListToggleLabel() {
        return this.showEventList ? 'Hide Events' : 'Show Events';
    }

    get sortedEvents() {
        if (!this.events) return [];

        return this.events
            .map(event => ({
                ...event,
                formattedDateTime: this.formatEventDateTime(event),
                priorityClass: `priority-${event.priority}`,
                cssClass: `event-dot event-${event.type}`
            }))
            .sort((a, b) => new Date(a.startDate) - new Date(b.startDate));
    }

    get modalTitle() {
        return this.isEditMode ? 'Edit Event' : 'Add New Event';
    }

    get saveButtonLabel() {
        return this.isEditMode ? 'Update Event' : 'Create Event';
    }

    // Helper methods
    formatDateToString(date) {
        // Format date as YYYY-MM-DD in local timezone (avoiding UTC conversion)
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    formatEventDateTime(event) {
        const startDate = new Date(event.startDate);
        const dateStr = startDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });

        if (event.startTime) {
            const timeStr = this.formatTime(event.startTime);
            return `${dateStr} at ${timeStr}`;
        }

        return dateStr;
    }

    formatTime(timeString) {
        if (!timeString) return '';

        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;

        return `${displayHour}:${minutes} ${ampm}`;
    }



    generateId() {
        return 'event' + Date.now() + Math.random().toString(36).substring(2, 11);
    }

    // Event handlers
    handleGoToToday() {
        if (this.calendar) {
            this.calendar.navigate(new Date());
        }
    }

    handleToggleEventList() {
        this.showEventList = !this.showEventList;
    }

    handleEventClick(event) {
        event.stopPropagation();
        const eventId = event.currentTarget.dataset.eventId;
        this.handleEditEventById(eventId);
    }

    handleAddEvent(preselectedDate = null) {
        this.isEditMode = false;
        this.currentEvent = {
            title: '',
            description: '',
            startDate: preselectedDate || (this.selectedDate ? this.formatDateToString(this.selectedDate) : this.formatDateToString(new Date())),
            endDate: preselectedDate || (this.selectedDate ? this.formatDateToString(this.selectedDate) : this.formatDateToString(new Date())),
            startTime: '09:00',
            endTime: '10:00',
            type: 'meeting',
            priority: 'medium'
        };
        this.showEventModal = true;
    }

    handleEditEvent(event) {
        const eventId = event.currentTarget.dataset.eventId;
        this.handleEditEventById(eventId);
    }

    handleEditEventById(eventId) {
        const eventToEdit = this.events.find(e => e.id === eventId);
        if (eventToEdit) {
            this.isEditMode = true;
            this.currentEvent = { ...eventToEdit };
            this.showEventModal = true;
        }
    }

    handleDeleteEvent(event) {
        const eventId = event.currentTarget.dataset.eventId;

        // Show confirmation
        if (confirm('Are you sure you want to delete this event?')) {
            this.deleteEvent(eventId);
        }
    }

    handleInputChange(event) {
        const field = event.target.name;
        const value = event.target.value;

        this.currentEvent = {
            ...this.currentEvent,
            [field]: value
        };
    }

    handleSaveEvent() {
        // Validate required fields
        if (!this.currentEvent.title || !this.currentEvent.startDate || !this.currentEvent.endDate) {
            this.showToast('Error', 'Please fill in all required fields', 'error');
            return;
        }

        // Validate date range
        if (new Date(this.currentEvent.endDate) < new Date(this.currentEvent.startDate)) {
            this.showToast('Error', 'End date cannot be before start date', 'error');
            return;
        }

        if (this.isEditMode) {
            this.updateEvent();
        } else {
            this.createEvent();
        }
    }

    handleCloseModal() {
        this.showEventModal = false;
        this.currentEvent = {};
        this.isEditMode = false;
    }

    // CRUD Operations
    createEvent() {
        try {
            const newEvent = {
                ...this.currentEvent,
                id: this.generateId()
            };

            this.events = [...this.events, newEvent];

            // Update Mobiscroll calendar
            this.updateMobiscrollEvents();

            this.showToast('Success', 'Event created successfully', 'success');
            this.handleCloseModal();
        } catch (error) {
            console.error('Error creating event:', error);
            this.showToast('Error', 'Failed to create event', 'error');
        }
    }

    updateEvent() {
        try {
            const eventIndex = this.events.findIndex(e => e.id === this.currentEvent.id);
            if (eventIndex !== -1) {
                this.events = [
                    ...this.events.slice(0, eventIndex),
                    { ...this.currentEvent },
                    ...this.events.slice(eventIndex + 1)
                ];

                // Update Mobiscroll calendar
                this.updateMobiscrollEvents();

                this.showToast('Success', 'Event updated successfully', 'success');
                this.handleCloseModal();
            }
        } catch (error) {
            console.error('Error updating event:', error);
            this.showToast('Error', 'Failed to update event', 'error');
        }
    }

    deleteEvent(eventId) {
        try {
            this.events = this.events.filter(e => e.id !== eventId);

            // Update Mobiscroll calendar
            this.updateMobiscrollEvents();

            this.showToast('Success', 'Event deleted successfully', 'success');
        } catch (error) {
            console.error('Error deleting event:', error);
            this.showToast('Error', 'Failed to delete event', 'error');
        }
    }

    // Utility methods
    showToast(title, message, variant) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: title,
                message: message,
                variant: variant
            })
        );
    }
}
