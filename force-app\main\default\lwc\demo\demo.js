import { LightningElement, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { loadScript } from 'lightning/platformResourceLoader';
import DAYJS_RESOURCE from '@salesforce/resourceUrl/dayjs';
const eventsData = [
    {
        "id": "event1",
        "title": "Team Meeting",
        "description": "Weekly team sync meeting",
        "startDate": "2025-06-23",
        "endDate": "2025-06-23",
        "startTime": "09:00",
        "endTime": "10:00",
        "type": "meeting",
        "priority": "high"
    },
    {
        "id": "event2",
        "title": "Project Deadline",
        "description": "Final submission for Q2 project",
        "startDate": "2025-06-25",
        "endDate": "2025-06-25",
        "startTime": "17:00",
        "endTime": "17:30",
        "type": "deadline",
        "priority": "high"
    },
    {
        "id": "event3",
        "title": "Client Presentation",
        "description": "Present new features to client",
        "startDate": "2025-06-27",
        "endDate": "2025-06-27",
        "startTime": "14:00",
        "endTime": "15:30",
        "type": "presentation",
        "priority": "medium"
    },
    {
        "id": "event4",
        "title": "Training Session",
        "description": "Salesforce Lightning Web Components training",
        "startDate": "2025-06-30",
        "endDate": "2025-06-30",
        "startTime": "10:00",
        "endTime": "12:00",
        "type": "training",
        "priority": "low"
    },
    {
        "id": "event5",
        "title": "Code Review",
        "description": "Review pull requests and code quality",
        "startDate": "2025-07-02",
        "endDate": "2025-07-02",
        "startTime": "11:00",
        "endTime": "12:00",
        "type": "review",
        "priority": "medium"
    }
];

export default class Demo extends LightningElement {
    @track events = [];
    @track currentDate = new Date();
    @track selectedDate = null;
    @track isMonthView = true;
    @track showEventModal = false;
    @track currentEvent = {};
    @track isEditMode = false;
    @track dayjsLoaded = false;

    // Calendar data
    weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Form options
    eventTypeOptions = [
        { label: 'Meeting', value: 'meeting' },
        { label: 'Deadline', value: 'deadline' },
        { label: 'Presentation', value: 'presentation' },
        { label: 'Training', value: 'training' },
        { label: 'Review', value: 'review' },
        { label: 'Other', value: 'other' }
    ];

    priorityOptions = [
        { label: 'High', value: 'high' },
        { label: 'Medium', value: 'medium' },
        { label: 'Low', value: 'low' }
    ];

    connectedCallback() {
        this.loadDayjs()
            .then(() => {
                this.dayjsLoaded = true;
                this.loadEvents();
            })
            .catch(error => {
                console.error('Error loading Day.js:', error);
                this.loadEvents(); // Fallback to native Date
            });
    }

    // Load Day.js library
    async loadDayjs() {
        try {
            await loadScript(this, DAYJS_RESOURCE);
            // Day.js is now available as window.dayjs
            this.dayjs = window.dayjs;
        } catch (error) {
            console.error('Failed to load Day.js:', error);
            throw error;
        }
    }

    // Initialize events with local data
    loadEvents() { // Load initial events from local JSON
        try {
            // Use the imported local JSON data directly
            this.events = eventsData;
        } catch (error) {
            console.error('Error loading initial events:', error);
            this.events = []; // Fallback to empty array
        }
    }

    // Computed properties
    get currentMonthYear() {
        if (this.dayjsLoaded && this.dayjs) {
            return this.dayjs(this.currentDate).format('MMMM YYYY');
        }
        // Fallback to native Date
        const options = { year: 'numeric', month: 'long' };
        return this.currentDate.toLocaleDateString('en-US', options);
    }

    get monthViewVariant() {
        return this.isMonthView ? 'brand' : 'neutral';
    }

    get listViewVariant() {
        return !this.isMonthView ? 'brand' : 'neutral';
    }

    get isListView() {
        return !this.isMonthView;
    }

    get hasEvents() {
        return this.events && this.events.length > 0;
    }

    get sortedEvents() {
        if (!this.events) return [];

        return this.events
            .map(event => ({
                ...event,
                formattedDateTime: this.formatEventDateTime(event),
                priorityClass: `priority-${event.priority}`,
                cssClass: `event-dot event-${event.type}`
            }))
            .sort((a, b) => {
                if (this.dayjsLoaded && this.dayjs) {
                    return this.dayjs(a.startDate).valueOf() - this.dayjs(b.startDate).valueOf();
                }
                // Fallback to native Date
                return new Date(a.startDate) - new Date(b.startDate);
            });
    }

    get calendarWeeks() {
        if (this.dayjsLoaded && this.dayjs) {
            return this.generateCalendarWeeksWithDayjs();
        }
        // Fallback to native Date implementation
        return this.generateCalendarWeeksNative();
    }

    generateCalendarWeeksWithDayjs() {
        const weeks = [];
        const currentMonth = this.dayjs(this.currentDate);

        // Get first and last day of the month
        const firstDay = currentMonth.startOf('month');
        const lastDay = currentMonth.endOf('month');

        // Get the start of the week containing the first day
        const startDate = firstDay.startOf('week');

        // Get the end of the week containing the last day
        const endDate = lastDay.endOf('week');

        let currentWeekStart = startDate;
        let weekIndex = 0;

        while (currentWeekStart.isBefore(endDate) || currentWeekStart.isSame(endDate, 'day')) {
            const week = {
                id: `week-${weekIndex}`,
                days: []
            };

            for (let i = 0; i < 7; i++) {
                const dayDate = currentWeekStart.add(i, 'day');

                const dayEvents = this.getEventsForDate(dayDate.toDate());
                const isCurrentMonth = dayDate.month() === currentMonth.month();
                const isToday = dayDate.isSame(this.dayjs(), 'day');
                const isSelected = this.selectedDate && dayDate.isSame(this.dayjs(this.selectedDate), 'day');

                week.days.push({
                    id: `day-${dayDate.valueOf()}`,
                    date: dayDate.format('YYYY-MM-DD'),
                    dayNumber: dayDate.date(),
                    hasEvents: dayEvents.length > 0,
                    events: dayEvents.map(event => ({
                        ...event,
                        cssClass: `event-dot event-${event.type}`
                    })),
                    cssClass: this.getDayCssClass(isCurrentMonth, isToday, isSelected)
                });
            }

            weeks.push(week);
            currentWeekStart = currentWeekStart.add(7, 'day');
            weekIndex++;
        }

        return weeks;
    }

    generateCalendarWeeksNative() {
        const weeks = [];
        const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
        const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);

        // Start from the beginning of the week containing the first day
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - startDate.getDay());

        // End at the end of the week containing the last day
        const endDate = new Date(lastDay);
        endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

        let currentWeekStart = new Date(startDate);
        let weekIndex = 0;

        while (currentWeekStart <= endDate) {
            const week = {
                id: `week-${weekIndex}`,
                days: []
            };

            for (let i = 0; i < 7; i++) {
                const dayDate = new Date(currentWeekStart);
                dayDate.setDate(currentWeekStart.getDate() + i);

                const dayEvents = this.getEventsForDate(dayDate);
                const isCurrentMonth = dayDate.getMonth() === this.currentDate.getMonth();
                const isToday = this.isToday(dayDate);
                const isSelected = this.selectedDate && this.isSameDate(dayDate, this.selectedDate);

                week.days.push({
                    id: `day-${dayDate.getTime()}`,
                    date: this.formatDateToString(dayDate),
                    dayNumber: dayDate.getDate(),
                    hasEvents: dayEvents.length > 0,
                    events: dayEvents.map(event => ({
                        ...event,
                        cssClass: `event-dot event-${event.type}`
                    })),
                    cssClass: this.getDayCssClass(isCurrentMonth, isToday, isSelected)
                });
            }

            weeks.push(week);
            currentWeekStart.setDate(currentWeekStart.getDate() + 7);
            weekIndex++;
        }

        return weeks;
    }

    get modalTitle() {
        return this.isEditMode ? 'Edit Event' : 'Add New Event';
    }

    get saveButtonLabel() {
        return this.isEditMode ? 'Update Event' : 'Create Event';
    }

    // Helper methods
    formatDateToString(date) {
        if (this.dayjsLoaded && this.dayjs) {
            return this.dayjs(date).format('YYYY-MM-DD');
        }
        // Fallback to native Date formatting
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    formatEventDateTime(event) {
        if (this.dayjsLoaded && this.dayjs) {
            const startDate = this.dayjs(event.startDate);
            let dateStr = startDate.format('MMM D, YYYY');

            if (event.startTime) {
                const timeStr = this.formatTime(event.startTime);
                return `${dateStr} at ${timeStr}`;
            }

            return dateStr;
        }

        // Fallback to native Date
        const startDate = new Date(event.startDate);
        const dateStr = startDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });

        if (event.startTime) {
            const timeStr = this.formatTime(event.startTime);
            return `${dateStr} at ${timeStr}`;
        }

        return dateStr;
    }

    formatTime(timeString) {
        if (!timeString) return '';

        if (this.dayjsLoaded && this.dayjs) {
            // Create a dayjs object with the time
            const timeObj = this.dayjs(`2000-01-01 ${timeString}`);
            return timeObj.format('h:mm A');
        }

        // Fallback to manual formatting
        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;

        return `${displayHour}:${minutes} ${ampm}`;
    }

    getEventsForDate(date) {
        if (!this.events) return [];

        const dateStr = this.formatDateToString(date);
        return this.events.filter(event => {
            if (this.dayjsLoaded && this.dayjs) {
                const eventStart = this.dayjs(event.startDate);
                const eventEnd = event.endDate ? this.dayjs(event.endDate) : eventStart;
                const checkDate = this.dayjs(dateStr);

                return checkDate.isSame(eventStart, 'day') ||
                       (checkDate.isAfter(eventStart, 'day') && checkDate.isBefore(eventEnd, 'day')) ||
                       checkDate.isSame(eventEnd, 'day');
            }

            // Fallback to string comparison
            return event.startDate === dateStr ||
                   (event.endDate && dateStr >= event.startDate && dateStr <= event.endDate);
        });
    }

    isToday(date) {
        if (this.dayjsLoaded && this.dayjs) {
            return this.dayjs(date).isSame(this.dayjs(), 'day');
        }
        // Fallback to native Date
        const today = new Date();
        return this.isSameDate(date, today);
    }

    isSameDate(date1, date2) {
        if (this.dayjsLoaded && this.dayjs) {
            return this.dayjs(date1).isSame(this.dayjs(date2), 'day');
        }
        // Fallback to native Date
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }

    getDayCssClass(isCurrentMonth, isToday, isSelected) {
        let cssClass = 'calendar-day';

        if (!isCurrentMonth) {
            cssClass += ' other-month';
        }

        if (isToday) {
            cssClass += ' today';
        }

        if (isSelected) {
            cssClass += ' selected';
        }

        return cssClass;
    }

    generateId() {
        return 'event' + Date.now() + Math.random().toString(36).substring(2, 11);
    }

    // Event handlers
    handlePreviousMonth() {
        if (this.dayjsLoaded && this.dayjs) {
            this.currentDate = this.dayjs(this.currentDate).subtract(1, 'month').startOf('month').toDate();
        } else {
            // Fallback to native Date
            this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
        }
    }

    handleNextMonth() {
        if (this.dayjsLoaded && this.dayjs) {
            this.currentDate = this.dayjs(this.currentDate).add(1, 'month').startOf('month').toDate();
        } else {
            // Fallback to native Date
            this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
        }
    }

    handleGoToToday() {
        if (this.dayjsLoaded && this.dayjs) {
            const today = this.dayjs();
            this.currentDate = today.toDate();
            this.selectedDate = today.toDate();
        } else {
            // Fallback to native Date
            this.currentDate = new Date();
            this.selectedDate = new Date();
        }
    }

    handleMonthView() {
        this.isMonthView = true;
    }

    handleListView() {
        this.isMonthView = false;
    }

    handleDayClick(event) {
        const dateStr = event.currentTarget.dataset.date;

        if (this.dayjsLoaded && this.dayjs) {
            this.selectedDate = this.dayjs(dateStr).toDate();
        } else {
            // Fallback: Fix timezone issue by creating date in local timezone
            const [year, month, day] = dateStr.split('-').map(num => parseInt(num));
            this.selectedDate = new Date(year, month - 1, day); // month is 0-indexed
        }

        // If double-click, open add event modal
        if (event.detail === 2) {
            this.handleAddEvent(dateStr);
        }
    }

    handleEventClick(event) {
        event.stopPropagation();
        const eventId = event.currentTarget.dataset.eventId;
        this.handleEditEventById(eventId);
    }

    handleAddEvent(preselectedDate = null) {
        this.isEditMode = false;

        let defaultDate;
        if (preselectedDate) {
            defaultDate = preselectedDate;
        } else if (this.selectedDate) {
            defaultDate = this.formatDateToString(this.selectedDate);
        } else {
            if (this.dayjsLoaded && this.dayjs) {
                defaultDate = this.dayjs().format('YYYY-MM-DD');
            } else {
                defaultDate = new Date().toISOString().split('T')[0];
            }
        }

        this.currentEvent = {
            title: '',
            description: '',
            startDate: defaultDate,
            endDate: defaultDate,
            startTime: '09:00',
            endTime: '10:00',
            type: 'meeting',
            priority: 'medium'
        };
        this.showEventModal = true;
    }

    handleEditEvent(event) {
        const eventId = event.currentTarget.dataset.eventId;
        this.handleEditEventById(eventId);
    }

    handleEditEventById(eventId) {
        const eventToEdit = this.events.find(e => e.id === eventId);
        if (eventToEdit) {
            this.isEditMode = true;
            this.currentEvent = { ...eventToEdit };
            this.showEventModal = true;
        }
    }

    handleDeleteEvent(event) {
        const eventId = event.currentTarget.dataset.eventId;

        // Show confirmation
        if (confirm('Are you sure you want to delete this event?')) {
            this.deleteEvent(eventId);
        }
    }

    handleInputChange(event) {
        const field = event.target.name;
        const value = event.target.value;

        this.currentEvent = {
            ...this.currentEvent,
            [field]: value
        };
    }

    handleSaveEvent() {
        // Validate required fields
        if (!this.currentEvent.title || !this.currentEvent.startDate || !this.currentEvent.endDate) {
            this.showToast('Error', 'Please fill in all required fields', 'error');
            return;
        }

        // Validate date range
        let isEndBeforeStart = false;
        if (this.dayjsLoaded && this.dayjs) {
            const startDate = this.dayjs(this.currentEvent.startDate);
            const endDate = this.dayjs(this.currentEvent.endDate);
            isEndBeforeStart = endDate.isBefore(startDate, 'day');
        } else {
            // Fallback to native Date
            isEndBeforeStart = new Date(this.currentEvent.endDate) < new Date(this.currentEvent.startDate);
        }

        if (isEndBeforeStart) {
            this.showToast('Error', 'End date cannot be before start date', 'error');
            return;
        }

        if (this.isEditMode) {
            this.updateEvent();
        } else {
            this.createEvent();
        }
    }

    handleCloseModal() {
        this.showEventModal = false;
        this.currentEvent = {};
        this.isEditMode = false;
    }

    // CRUD Operations
    createEvent() {
        try {
            const newEvent = {
                ...this.currentEvent,
                id: this.generateId()
            };

            this.events = [...this.events, newEvent];
            this.showToast('Success', 'Event created successfully', 'success');
            this.handleCloseModal();
        } catch (error) {
            console.error('Error creating event:', error);
            this.showToast('Error', 'Failed to create event', 'error');
        }
    }

    updateEvent() {
        try {
            const eventIndex = this.events.findIndex(e => e.id === this.currentEvent.id);
            if (eventIndex !== -1) {
                this.events = [
                    ...this.events.slice(0, eventIndex),
                    { ...this.currentEvent },
                    ...this.events.slice(eventIndex + 1)
                ];
                this.showToast('Success', 'Event updated successfully', 'success');
                this.handleCloseModal();
            }
        } catch (error) {
            console.error('Error updating event:', error);
            this.showToast('Error', 'Failed to update event', 'error');
        }
    }

    deleteEvent(eventId) {
        try {
            this.events = this.events.filter(e => e.id !== eventId);
            this.showToast('Success', 'Event deleted successfully', 'success');
        } catch (error) {
            console.error('Error deleting event:', error);
            this.showToast('Error', 'Failed to delete event', 'error');
        }
    }

    // Utility methods
    showToast(title, message, variant) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: title,
                message: message,
                variant: variant
            })
        );
    }
}
