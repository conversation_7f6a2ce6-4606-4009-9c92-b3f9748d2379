import { LightningElement, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
const eventsData = [
    {
        "id": "event1",
        "title": "Team Meeting",
        "description": "Weekly team sync meeting",
        "startDate": "2025-06-23",
        "endDate": "2025-06-23",
        "startTime": "09:00",
        "endTime": "10:00",
        "type": "meeting",
        "priority": "high"
    },
    {
        "id": "event2",
        "title": "Project Deadline",
        "description": "Final submission for Q2 project",
        "startDate": "2025-06-25",
        "endDate": "2025-06-25",
        "startTime": "17:00",
        "endTime": "17:30",
        "type": "deadline",
        "priority": "high"
    },
    {
        "id": "event3",
        "title": "Client Presentation",
        "description": "Present new features to client",
        "startDate": "2025-06-27",
        "endDate": "2025-06-27",
        "startTime": "14:00",
        "endTime": "15:30",
        "type": "presentation",
        "priority": "medium"
    },
    {
        "id": "event4",
        "title": "Training Session",
        "description": "Salesforce Lightning Web Components training",
        "startDate": "2025-06-30",
        "endDate": "2025-06-30",
        "startTime": "10:00",
        "endTime": "12:00",
        "type": "training",
        "priority": "low"
    },
    {
        "id": "event5",
        "title": "Code Review",
        "description": "Review pull requests and code quality",
        "startDate": "2025-07-02",
        "endDate": "2025-07-02",
        "startTime": "11:00",
        "endTime": "12:00",
        "type": "review",
        "priority": "medium"
    }
]
    ;  

export default class Demo extends LightningElement {
    @track events = [];
    @track currentDate = new Date();
    @track selectedDate = null;
    @track isMonthView = true;
    @track showEventModal = false;
    @track currentEvent = {};
    @track isEditMode = false;

    // Calendar data
    weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Form options
    eventTypeOptions = [
        { label: 'Meeting', value: 'meeting' },
        { label: 'Deadline', value: 'deadline' },
        { label: 'Presentation', value: 'presentation' },
        { label: 'Training', value: 'training' },
        { label: 'Review', value: 'review' },
        { label: 'Other', value: 'other' }
    ];

    priorityOptions = [
        { label: 'High', value: 'high' },
        { label: 'Medium', value: 'medium' },
        { label: 'Low', value: 'low' }
    ];

    connectedCallback() {
        this.loadEvents();
    }

    // Initialize events with local data
    loadEvents() { // Load initial events from local JSON
        try {
            // Use the imported local JSON data directly
            this.events = eventsData;
        } catch (error) {
            console.error('Error loading initial events:', error);
            this.events = []; // Fallback to empty array
        }
    }

    // Computed properties
    get currentMonthYear() {
        const options = { year: 'numeric', month: 'long' };
        return this.currentDate.toLocaleDateString('en-US', options);
    }

    get monthViewVariant() {
        return this.isMonthView ? 'brand' : 'neutral';
    }

    get listViewVariant() {
        return !this.isMonthView ? 'brand' : 'neutral';
    }

    get isListView() {
        return !this.isMonthView;
    }

    get hasEvents() {
        return this.events && this.events.length > 0;
    }

    get sortedEvents() {
        if (!this.events) return [];

        return this.events
            .map(event => ({
                ...event,
                formattedDateTime: this.formatEventDateTime(event),
                priorityClass: `priority-${event.priority}`,
                cssClass: `event-dot event-${event.type}`
            }))
            .sort((a, b) => new Date(a.startDate) - new Date(b.startDate));
    }

    get calendarWeeks() {
        const weeks = [];
        const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
        const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);

        // Start from the beginning of the week containing the first day
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - startDate.getDay());

        // End at the end of the week containing the last day
        const endDate = new Date(lastDay);
        endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

        let currentWeekStart = new Date(startDate);
        let weekIndex = 0;

        while (currentWeekStart <= endDate) {
            const week = {
                id: `week-${weekIndex}`,
                days: []
            };

            for (let i = 0; i < 7; i++) {
                const dayDate = new Date(currentWeekStart);
                dayDate.setDate(currentWeekStart.getDate() + i);

                const dayEvents = this.getEventsForDate(dayDate);
                const isCurrentMonth = dayDate.getMonth() === this.currentDate.getMonth();
                const isToday = this.isToday(dayDate);
                const isSelected = this.selectedDate && this.isSameDate(dayDate, this.selectedDate);

                week.days.push({
                    id: `day-${dayDate.getTime()}`,
                    date: dayDate.toISOString().split('T')[0],
                    dayNumber: dayDate.getDate(),
                    hasEvents: dayEvents.length > 0,
                    events: dayEvents.map(event => ({
                        ...event,
                        cssClass: `event-dot event-${event.type}`
                    })),
                    cssClass: this.getDayCssClass(isCurrentMonth, isToday, isSelected)
                });
            }

            weeks.push(week);
            currentWeekStart.setDate(currentWeekStart.getDate() + 7);
            weekIndex++;
        }

        return weeks;
    }

    get modalTitle() {
        return this.isEditMode ? 'Edit Event' : 'Add New Event';
    }

    get saveButtonLabel() {
        return this.isEditMode ? 'Update Event' : 'Create Event';
    }

    // Helper methods
    formatEventDateTime(event) {
        const startDate = new Date(event.startDate);
        const dateStr = startDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });

        if (event.startTime) {
            const timeStr = this.formatTime(event.startTime);
            return `${dateStr} at ${timeStr}`;
        }

        return dateStr;
    }

    formatTime(timeString) {
        if (!timeString) return '';

        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;

        return `${displayHour}:${minutes} ${ampm}`;
    }

    getEventsForDate(date) {
        if (!this.events) return [];

        const dateStr = date.toISOString().split('T')[0];
        return this.events.filter(event =>
            event.startDate === dateStr ||
            (event.endDate && dateStr >= event.startDate && dateStr <= event.endDate)
        );
    }

    isToday(date) {
        const today = new Date();
        return this.isSameDate(date, today);
    }

    isSameDate(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }

    getDayCssClass(isCurrentMonth, isToday, isSelected) {
        let cssClass = 'calendar-day';

        if (!isCurrentMonth) {
            cssClass += ' other-month';
        }

        if (isToday) {
            cssClass += ' today';
        }

        if (isSelected) {
            cssClass += ' selected';
        }

        return cssClass;
    }

    generateId() {
        return 'event' + Date.now() + Math.random().toString(36).substring(2, 11);
    }

    // Event handlers
    handlePreviousMonth() {
        this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
    }

    handleNextMonth() {
        this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
    }

    handleGoToToday() {
        this.currentDate = new Date();
        this.selectedDate = new Date();
    }

    handleMonthView() {
        this.isMonthView = true;
    }

    handleListView() {
        this.isMonthView = false;
    }

    handleDayClick(event) {
        const dateStr = event.currentTarget.dataset.date;
        this.selectedDate = new Date(dateStr);

        // If double-click, open add event modal
        if (event.detail === 2) {
            this.handleAddEvent(dateStr);
        }
    }

    handleEventClick(event) {
        event.stopPropagation();
        const eventId = event.currentTarget.dataset.eventId;
        this.handleEditEventById(eventId);
    }

    handleAddEvent(preselectedDate = null) {
        this.isEditMode = false;
        this.currentEvent = {
            title: '',
            description: '',
            startDate: preselectedDate || (this.selectedDate ? this.selectedDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]),
            endDate: preselectedDate || (this.selectedDate ? this.selectedDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]),
            startTime: '09:00',
            endTime: '10:00',
            type: 'meeting',
            priority: 'medium'
        };
        this.showEventModal = true;
    }

    handleEditEvent(event) {
        const eventId = event.currentTarget.dataset.eventId;
        this.handleEditEventById(eventId);
    }

    handleEditEventById(eventId) {
        const eventToEdit = this.events.find(e => e.id === eventId);
        if (eventToEdit) {
            this.isEditMode = true;
            this.currentEvent = { ...eventToEdit };
            this.showEventModal = true;
        }
    }

    handleDeleteEvent(event) {
        const eventId = event.currentTarget.dataset.eventId;

        // Show confirmation
        if (confirm('Are you sure you want to delete this event?')) {
            this.deleteEvent(eventId);
        }
    }

    handleInputChange(event) {
        const field = event.target.name;
        const value = event.target.value;

        this.currentEvent = {
            ...this.currentEvent,
            [field]: value
        };
    }

    handleSaveEvent() {
        // Validate required fields
        if (!this.currentEvent.title || !this.currentEvent.startDate || !this.currentEvent.endDate) {
            this.showToast('Error', 'Please fill in all required fields', 'error');
            return;
        }

        // Validate date range
        if (new Date(this.currentEvent.endDate) < new Date(this.currentEvent.startDate)) {
            this.showToast('Error', 'End date cannot be before start date', 'error');
            return;
        }

        if (this.isEditMode) {
            this.updateEvent();
        } else {
            this.createEvent();
        }
    }

    handleCloseModal() {
        this.showEventModal = false;
        this.currentEvent = {};
        this.isEditMode = false;
    }

    // CRUD Operations
    createEvent() {
        try {
            const newEvent = {
                ...this.currentEvent,
                id: this.generateId()
            };

            this.events = [...this.events, newEvent];
            this.showToast('Success', 'Event created successfully', 'success');
            this.handleCloseModal();
        } catch (error) {
            console.error('Error creating event:', error);
            this.showToast('Error', 'Failed to create event', 'error');
        }
    }

    updateEvent() {
        try {
            const eventIndex = this.events.findIndex(e => e.id === this.currentEvent.id);
            if (eventIndex !== -1) {
                this.events = [
                    ...this.events.slice(0, eventIndex),
                    { ...this.currentEvent },
                    ...this.events.slice(eventIndex + 1)
                ];
                this.showToast('Success', 'Event updated successfully', 'success');
                this.handleCloseModal();
            }
        } catch (error) {
            console.error('Error updating event:', error);
            this.showToast('Error', 'Failed to update event', 'error');
        }
    }

    deleteEvent(eventId) {
        try {
            this.events = this.events.filter(e => e.id !== eventId);
            this.showToast('Success', 'Event deleted successfully', 'success');
        } catch (error) {
            console.error('Error deleting event:', error);
            this.showToast('Error', 'Failed to delete event', 'error');
        }
    }

    // Utility methods
    showToast(title, message, variant) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: title,
                message: message,
                variant: variant
            })
        );
    }
}
